#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小区峰值利用率统计工具
支持两种Excel工作簿格式的数据处理和分析
"""

import pandas as pd
import numpy as np
import os
import glob
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from datetime import datetime, timedelta
import threading
from openpyxl import Workbook
from openpyxl.styles import PatternFill
import re
from tkcalendar import DateEntry

class CellUtilizationAnalyzer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("小区峰值利用率统计工具")
        self.root.geometry("800x600")

        # 数据存储
        self.processed_data = []
        self.summary_data = None

        # 创建界面
        self.create_widgets()

    def create_widgets(self):
        """创建用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 左侧框架
        left_frame = ttk.Frame(main_frame)
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        # 右侧框架
        right_frame = ttk.Frame(main_frame)
        right_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 文件选择区域（左侧）
        file_frame = ttk.LabelFrame(left_frame, text="文件选择", padding="10")
        file_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 按钮框架
        button_frame = ttk.Frame(file_frame)
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(button_frame, text="选择Excel文件",
                  command=self.select_files).grid(row=0, column=0, padx=(0, 10))

        ttk.Button(button_frame, text="选择文件夹",
                  command=self.select_folder).grid(row=0, column=1, padx=(0, 10))

        ttk.Button(button_frame, text="清空列表",
                  command=self.clear_files).grid(row=0, column=2)

        # 文件列表（扩大）
        list_frame = ttk.Frame(file_frame)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.file_list = tk.Listbox(list_frame, height=15)
        scrollbar_files = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_list.yview)
        self.file_list.configure(yscrollcommand=scrollbar_files.set)

        self.file_list.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_files.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 配置文件选择区域的网格权重
        file_frame.columnconfigure(0, weight=1)
        file_frame.rowconfigure(1, weight=1)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        # 处理选项区域（右侧）
        options_frame = ttk.LabelFrame(right_frame, text="处理选项", padding="10")
        options_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N), pady=(0, 10))

        # 数据类型选择
        self.data_type = tk.StringVar(value="daily")
        ttk.Radiobutton(options_frame, text="每日峰值利用率",
                       variable=self.data_type, value="daily").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        ttk.Radiobutton(options_frame, text="平均峰值利用率",
                       variable=self.data_type, value="average").grid(row=1, column=0, sticky=tk.W, pady=(0, 10))

        # 日期范围选择
        date_frame = ttk.LabelFrame(options_frame, text="日期范围选择", padding="10")
        date_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        # 设置默认日期（昨天和今天）
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)

        ttk.Label(date_frame, text="开始日期:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.start_date_picker = DateEntry(date_frame, width=12, background='darkblue',
                                         foreground='white', borderwidth=2, date_pattern='yyyy-mm-dd')
        self.start_date_picker.set_date(yesterday)
        self.start_date_picker.grid(row=0, column=1, sticky=tk.W, pady=(0, 5), padx=(10, 0))

        ttk.Label(date_frame, text="结束日期:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        self.end_date_picker = DateEntry(date_frame, width=12, background='darkblue',
                                       foreground='white', borderwidth=2, date_pattern='yyyy-mm-dd')
        self.end_date_picker.set_date(today)
        self.end_date_picker.grid(row=1, column=1, sticky=tk.W, pady=(5, 0), padx=(10, 0))

        # 处理按钮（右侧）
        process_button_frame = ttk.Frame(right_frame)
        process_button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(process_button_frame, text="开始处理",
                  command=self.start_processing).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(process_button_frame, text="导出结果",
                  command=self.export_results).grid(row=0, column=1)

        # 进度条（右侧）
        self.progress = ttk.Progressbar(right_frame, mode='determinate')
        self.progress.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # 状态标签（右侧）
        self.status_label = ttk.Label(right_frame, text="就绪")
        self.status_label.grid(row=3, column=0, sticky=tk.W)

        # 结果显示区域（跨越两列）
        result_frame = ttk.LabelFrame(main_frame, text="处理结果", padding="10")
        result_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        # 创建Treeview来显示结果
        columns = ('日期', '小区名称', '峰值利用率', '小区类型', '预警状态')
        self.result_tree = ttk.Treeview(result_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.result_tree.heading(col, text=col)
            self.result_tree.column(col, width=120)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_tree.yview)
        self.result_tree.configure(yscrollcommand=scrollbar.set)

        self.result_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # 主框架权重
        main_frame.columnconfigure(0, weight=2)  # 左侧占更多空间
        main_frame.columnconfigure(1, weight=1)  # 右侧
        main_frame.rowconfigure(0, weight=1)     # 上半部分
        main_frame.rowconfigure(1, weight=2)     # 结果区域占更多空间

        # 左侧框架权重
        left_frame.columnconfigure(0, weight=1)
        left_frame.rowconfigure(0, weight=1)

        # 右侧框架权重
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(0, weight=0)  # 选项区域固定大小
        right_frame.rowconfigure(1, weight=0)  # 按钮区域固定大小
        right_frame.rowconfigure(2, weight=0)  # 进度条固定大小
        right_frame.rowconfigure(3, weight=0)  # 状态标签固定大小

        # 结果框架权重
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)

    def select_files(self):
        """选择Excel文件"""
        files = filedialog.askopenfilenames(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        for file in files:
            if file not in self.file_list.get(0, tk.END):
                self.file_list.insert(tk.END, file)

    def select_folder(self):
        """选择文件夹"""
        folder = filedialog.askdirectory(title="选择包含Excel文件的文件夹")
        if folder:
            excel_files = glob.glob(os.path.join(folder, "*.xlsx")) + glob.glob(os.path.join(folder, "*.xls"))
            for file in excel_files:
                if file not in self.file_list.get(0, tk.END):
                    self.file_list.insert(tk.END, file)

    def clear_files(self):
        """清空文件列表"""
        self.file_list.delete(0, tk.END)
        self.processed_data = []
        self.summary_data = None
        # 清空结果显示
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

    def get_cell_type(self, cell_name):
        """根据小区名称判断小区类型"""
        if "ZFH" in cell_name:
            return "FDD"
        elif "10D" in cell_name or "MIMO" in cell_name:
            return "3D-MIMO"
        else:
            return "普通"

    def get_warning_threshold(self, cell_type):
        """根据小区类型获取预警阈值"""
        thresholds = {
            "FDD": 0.70,
            "3D-MIMO": 0.80,
            "普通": 0.50
        }
        return thresholds.get(cell_type, 0.50)

    def is_high_load_warning(self, utilization, cell_type):
        """判断是否为高负荷预警"""
        threshold = self.get_warning_threshold(cell_type)
        return utilization >= threshold

    def extract_date_from_datetime(self, datetime_str):
        """从日期时间字符串中提取日期"""
        try:
            if pd.isna(datetime_str):
                return None
            dt = pd.to_datetime(datetime_str)
            return dt.date()
        except:
            return None

    def process_type1_workbook(self, file_path):
        """处理第一种格式的工作簿（sheet1和指标或计数器）"""
        try:
            # 读取sheet1工作表
            df = pd.read_excel(file_path, sheet_name='sheet1')

            # 检查必要的列是否存在
            required_cols = ['开始时间', '小区名称', '上行PRB平均利用率(集团)',
                           '下行PRB平均利用率(集团)', 'CCE利用率(PDCCH信道CCE占用率)']

            # 查找实际的列名（可能有变化）
            actual_cols = {}
            for col in required_cols:
                found_col = None
                for df_col in df.columns:
                    if col in df_col or df_col in col:
                        found_col = df_col
                        break
                if found_col:
                    actual_cols[col] = found_col
                else:
                    print(f"警告：在文件 {file_path} 中未找到列 {col}")
                    return []

            results = []
            for _, row in df.iterrows():
                try:
                    # 提取日期
                    date = self.extract_date_from_datetime(row[actual_cols['开始时间']])
                    if date is None:
                        continue

                    # 提取小区名称
                    cell_name = row[actual_cols['小区名称']]
                    if pd.isna(cell_name):
                        continue

                    # 提取三个利用率值
                    uplink_util = row[actual_cols['上行PRB平均利用率(集团)']]
                    downlink_util = row[actual_cols['下行PRB平均利用率(集团)']]
                    cce_util = row[actual_cols['CCE利用率(PDCCH信道CCE占用率)']]

                    # 转换为数值并计算峰值利用率（三者最大值）
                    utils = []
                    for util in [uplink_util, downlink_util, cce_util]:
                        if not pd.isna(util):
                            try:
                                utils.append(float(util))
                            except:
                                pass

                    if utils:
                        peak_util = max(utils)
                        cell_type = self.get_cell_type(str(cell_name))
                        is_warning = self.is_high_load_warning(peak_util, cell_type)

                        results.append({
                            'date': date,
                            'cell_name': str(cell_name),
                            'peak_utilization': peak_util,
                            'cell_type': cell_type,
                            'is_warning': is_warning,
                            'file_source': file_path
                        })
                except Exception as e:
                    print(f"处理行数据时出错: {e}")
                    continue

            return results

        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            return []

    def process_type2_workbook(self, file_path):
        """处理第二种格式的工作簿（Sheet0和指标(计数器)）"""
        try:
            # 读取Sheet0工作表
            df = pd.read_excel(file_path, sheet_name='Sheet0')

            # 检查必要的列是否存在
            required_cols = ['开始时间', 'E-UTRAN TDD小区名称']

            # 查找实际的列名
            actual_cols = {}
            for col in required_cols:
                found_col = None
                for df_col in df.columns:
                    if col in df_col or df_col in col:
                        found_col = df_col
                        break
                if found_col:
                    actual_cols[col] = found_col
                else:
                    print(f"警告：在文件 {file_path} 中未找到列 {col}")
                    return []

            # 查找包含"峰值利用率"的列
            peak_util_col = None
            for col in df.columns:
                if "峰值利用率" in col:
                    peak_util_col = col
                    break

            if peak_util_col is None:
                print(f"警告：在文件 {file_path} 中未找到包含'峰值利用率'的列")
                return []

            results = []
            for _, row in df.iterrows():
                try:
                    # 提取日期
                    date = self.extract_date_from_datetime(row[actual_cols['开始时间']])
                    if date is None:
                        continue

                    # 提取小区名称
                    cell_name = row[actual_cols['E-UTRAN TDD小区名称']]
                    if pd.isna(cell_name):
                        continue

                    # 提取峰值利用率
                    peak_util = row[peak_util_col]
                    if pd.isna(peak_util):
                        continue

                    try:
                        peak_util = float(peak_util)
                        cell_type = self.get_cell_type(str(cell_name))
                        is_warning = self.is_high_load_warning(peak_util, cell_type)

                        results.append({
                            'date': date,
                            'cell_name': str(cell_name),
                            'peak_utilization': peak_util,
                            'cell_type': cell_type,
                            'is_warning': is_warning,
                            'file_source': file_path
                        })
                    except:
                        continue

                except Exception as e:
                    print(f"处理行数据时出错: {e}")
                    continue

            return results

        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            return []

    def detect_workbook_type(self, file_path):
        """检测工作簿类型"""
        try:
            xl_file = pd.ExcelFile(file_path)
            sheet_names = xl_file.sheet_names

            # 检查是否为第一种格式
            if 'sheet1' in sheet_names and '指标或计数器' in sheet_names:
                return 1
            # 检查是否为第二种格式
            elif 'Sheet0' in sheet_names and '指标(计数器)' in sheet_names:
                return 2
            else:
                return 0  # 未知格式
        except:
            return 0

    def process_single_file(self, file_path):
        """处理单个文件"""
        workbook_type = self.detect_workbook_type(file_path)

        if workbook_type == 1:
            return self.process_type1_workbook(file_path)
        elif workbook_type == 2:
            return self.process_type2_workbook(file_path)
        else:
            print(f"未识别的工作簿格式: {file_path}")
            return []

    def start_processing(self):
        """开始处理文件"""
        files = list(self.file_list.get(0, tk.END))
        if not files:
            messagebox.showwarning("警告", "请先选择要处理的Excel文件")
            return

        # 在新线程中处理，避免界面冻结
        thread = threading.Thread(target=self.process_files, args=(files,))
        thread.daemon = True
        thread.start()

    def process_files(self, files):
        """处理文件列表"""
        self.processed_data = []
        total_files = len(files)

        self.root.after(0, lambda: self.progress.configure(maximum=total_files))
        self.root.after(0, lambda: self.progress.configure(value=0))

        for i, file_path in enumerate(files):
            self.root.after(0, lambda f=file_path: self.status_label.configure(text=f"正在处理: {os.path.basename(f)}"))

            file_data = self.process_single_file(file_path)
            self.processed_data.extend(file_data)

            self.root.after(0, lambda v=i+1: self.progress.configure(value=v))

        # 处理完成后的操作
        self.root.after(0, self.on_processing_complete)

    def on_processing_complete(self):
        """处理完成后的操作"""
        self.status_label.configure(text=f"处理完成，共处理 {len(self.processed_data)} 条记录")

        if self.data_type.get() == "average":
            self.calculate_average_utilization()
        else:
            self.summary_data = self.processed_data

        self.update_result_display()

    def calculate_average_utilization(self):
        """计算平均峰值利用率"""
        if not self.processed_data:
            return

        # 获取日期范围
        try:
            start_date = self.start_date_picker.get_date()
            end_date = self.end_date_picker.get_date()
        except Exception as e:
            messagebox.showerror("错误", f"获取日期失败: {e}")
            return

        # 过滤日期范围内的数据
        filtered_data = [
            record for record in self.processed_data
            if start_date <= record['date'] <= end_date
        ]

        if not filtered_data:
            messagebox.showwarning("警告", "指定日期范围内没有数据")
            return

        # 按小区分组计算平均值
        cell_groups = {}
        for record in filtered_data:
            cell_name = record['cell_name']
            if cell_name not in cell_groups:
                cell_groups[cell_name] = []
            cell_groups[cell_name].append(record)

        # 计算每个小区的平均峰值利用率
        self.summary_data = []
        for cell_name, records in cell_groups.items():
            avg_utilization = sum(r['peak_utilization'] for r in records) / len(records)
            cell_type = records[0]['cell_type']  # 小区类型应该是一致的
            is_warning = self.is_high_load_warning(avg_utilization, cell_type)

            self.summary_data.append({
                'date': f"{start_date} 至 {end_date}",
                'cell_name': cell_name,
                'peak_utilization': avg_utilization,
                'cell_type': cell_type,
                'is_warning': is_warning,
                'record_count': len(records)
            })

    def ask_date_range(self):
        """询问用户日期范围"""
        # 获取数据中的所有日期
        dates = sorted(set(record['date'] for record in self.processed_data))
        if not dates:
            return

        min_date = min(dates)
        max_date = max(dates)

        # 设置默认日期范围
        self.start_date_picker.set_date(min_date)
        self.end_date_picker.set_date(max_date)

        messagebox.showinfo("日期范围",
                          f"数据日期范围：{min_date} 至 {max_date}\n"
                          f"已自动设置日期选择器，您可以调整后重新点击'开始处理'")

    def update_result_display(self):
        """更新结果显示"""
        # 清空现有显示
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        if not self.summary_data:
            return

        # 添加新数据
        for record in self.summary_data:
            date_str = str(record['date'])
            utilization_str = f"{record['peak_utilization']:.2%}"
            warning_str = "高负荷预警" if record['is_warning'] else "正常"

            item = self.result_tree.insert('', 'end', values=(
                date_str,
                record['cell_name'],
                utilization_str,
                record['cell_type'],
                warning_str
            ))

            # 如果是高负荷预警，设置背景色
            if record['is_warning']:
                self.result_tree.set(item, '预警状态', warning_str)
                # 注意：tkinter的Treeview颜色设置比较复杂，这里先用文字标识

    def export_results(self):
        """导出结果到Excel文件"""
        if not self.summary_data:
            messagebox.showwarning("警告", "没有数据可导出，请先处理文件")
            return

        # 选择保存位置
        file_path = filedialog.asksaveasfilename(
            title="保存结果",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )

        if not file_path:
            return

        try:
            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "小区峰值利用率统计"

            # 设置表头
            headers = ['日期', '小区名称', '峰值利用率', '小区类型', '预警状态']
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # 定义预警颜色填充
            warning_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")  # 黄色

            # 填充数据
            for row, record in enumerate(self.summary_data, 2):
                ws.cell(row=row, column=1, value=str(record['date']))
                ws.cell(row=row, column=2, value=record['cell_name'])
                ws.cell(row=row, column=3, value=record['peak_utilization'])
                ws.cell(row=row, column=4, value=record['cell_type'])
                ws.cell(row=row, column=5, value="高负荷预警" if record['is_warning'] else "正常")

                # 如果是高负荷预警，标记整行
                if record['is_warning']:
                    for col in range(1, 6):
                        ws.cell(row=row, column=col).fill = warning_fill

            # 调整列宽
            column_widths = [15, 30, 15, 15, 15]
            for col, width in enumerate(column_widths, 1):
                ws.column_dimensions[chr(64 + col)].width = width

            # 保存文件
            wb.save(file_path)
            messagebox.showinfo("成功", f"结果已导出到: {file_path}")

        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {e}")

    def run(self):
        """运行应用程序"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = CellUtilizationAnalyzer()
        app.run()
    except Exception as e:
        print(f"程序运行出错: {e}")
        messagebox.showerror("错误", f"程序运行出错: {e}")

if __name__ == "__main__":
    main()