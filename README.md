# 小区峰值利用率统计工具

## 功能概述

这是一个专门用于统计Excel工作簿中小区峰值利用率的工具，支持两种不同的工作簿格式，具有可视化界面，能够自动识别高负荷预警小区。

## 支持的工作簿格式

### 第一种格式
- **工作表名称**：`sheet1` 和 `指标或计数器`
- **数据工作表**：`sheet1`
- **关键列**：
  - `开始时间`：包含日期时间信息（格式：2025-07-30 00:00:00）
  - `小区名称`：要查询的具体小区对象
  - `上行PRB平均利用率(集团)`：上行利用率数据
  - `下行PRB平均利用率(集团)`：下行利用率数据
  - `CCE利用率(PDCCH信道CCE占用率)`：CCE利用率数据
- **处理逻辑**：提取每个小区每个日期的峰值利用率（三个利用率的最大值）

### 第二种格式
- **工作表名称**：`Sheet0` 和 `指标(计数器)`
- **数据工作表**：`Sheet0`
- **关键列**：
  - `开始时间`：包含日期时间信息（格式：2025-07-30 00:00:00）
  - `E-UTRAN TDD小区名称`：要查询的具体小区对象
  - 包含`峰值利用率`字段的列：如`峰值利用率-TDD-YE`、`峰值利用率-FDD-YE`等
- **处理逻辑**：直接提取每个小区每个日期的峰值利用率

## 小区类型识别和预警规则

### 小区类型识别
- **FDD制式**：小区名称包含"ZFH"字符组合
- **3D-MIMO制式**：小区名称包含"10D"字符组合或"MIMO"字样
- **普通小区**：不包含以上特殊字符的小区

### 高负荷预警阈值
- **FDD制式小区**：峰值利用率 ≥ 70%
- **3D-MIMO制式小区**：峰值利用率 ≥ 80%
- **普通小区**：峰值利用率 ≥ 50%

## 使用方法

### 1. 启动程序
```bash
python cell_utilization_analyzer.py
```

### 2. 选择文件
- 点击"选择Excel文件"按钮选择单个或多个Excel文件
- 或点击"选择文件夹"按钮选择包含Excel文件的整个文件夹

### 3. 设置处理选项
- **每日峰值利用率**：显示每个小区每天的峰值利用率
- **平均峰值利用率**：计算指定日期范围内每个小区的平均峰值利用率
  - 选择此选项时，需要在日期范围输入框中指定开始和结束日期（格式：YYYY-MM-DD）

### 4. 开始处理
- 点击"开始处理"按钮开始分析数据
- 程序会显示处理进度和状态信息
- 处理完成后，结果会显示在下方的表格中

### 5. 查看结果
结果表格包含以下列：
- **日期**：数据日期或日期范围
- **小区名称**：小区的名称
- **峰值利用率**：计算得出的峰值利用率（百分比格式）
- **小区类型**：FDD、3D-MIMO或普通
- **预警状态**：正常或高负荷预警

### 6. 导出结果
- 点击"导出结果"按钮将结果保存为Excel文件
- 高负荷预警的小区会在Excel中用黄色背景标记

## 功能特点

1. **自动格式识别**：程序会自动识别Excel文件的格式类型
2. **批量处理**：支持同时处理多个Excel文件
3. **可视化界面**：简洁明了的图形用户界面
4. **进度显示**：实时显示处理进度
5. **高负荷预警**：自动识别并标记高负荷预警小区
6. **灵活的统计方式**：支持每日数据和平均数据两种统计方式
7. **Excel导出**：支持将结果导出为Excel格式，预警小区自动标色

## 依赖库

程序需要以下Python库：
- pandas：Excel文件读取和数据处理
- numpy：数值计算
- tkinter：图形用户界面（Python标准库）
- openpyxl：Excel文件写入和格式设置
- threading：多线程处理

## 安装依赖

```bash
pip install pandas numpy openpyxl
```

## 注意事项

1. 确保Excel文件没有被其他程序打开
2. 日期格式应为标准的日期时间格式
3. 利用率数据应为数值类型
4. 程序会跳过无效的数据行
5. 处理大量文件时请耐心等待

## 故障排除

如果遇到问题，请检查：
1. Excel文件格式是否符合要求
2. 必要的列是否存在
3. 数据格式是否正确
4. 文件是否损坏或被占用

## 测试

运行测试脚本验证功能：
```bash
python test_analyzer.py
```