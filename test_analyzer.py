#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试小区峰值利用率分析工具
"""

import pandas as pd
import glob
import os

def test_file_detection():
    """测试文件格式检测"""
    print("=== 测试文件格式检测 ===")

    excel_files = [f for f in glob.glob('*.xlsx') if not f.startswith('~$')]

    for file_path in excel_files:
        print(f"\n文件: {file_path}")
        try:
            xl_file = pd.ExcelFile(file_path)
            sheet_names = xl_file.sheet_names
            print(f"工作表: {sheet_names}")

            # 检测格式
            if 'sheet1' in sheet_names and '指标或计数器' in sheet_names:
                print("格式: 第一种格式 (sheet1 + 指标或计数器)")
            elif 'Sheet0' in sheet_names and '指标(计数器)' in sheet_names:
                print("格式: 第二种格式 (Sheet0 + 指标(计数器))")
            else:
                print("格式: 未知格式")

        except Exception as e:
            print(f"错误: {e}")

def test_data_processing():
    """测试数据处理功能"""
    print("\n=== 测试数据处理功能 ===")

    # 创建一个简化的分析器实例
    class SimpleAnalyzer:
        def get_cell_type(self, cell_name):
            if "ZFH" in cell_name:
                return "FDD"
            elif "10D" in cell_name or "MIMO" in cell_name:
                return "3D-MIMO"
            else:
                return "普通"

        def get_warning_threshold(self, cell_type):
            thresholds = {"FDD": 0.70, "3D-MIMO": 0.80, "普通": 0.50}
            return thresholds.get(cell_type, 0.50)

        def is_high_load_warning(self, utilization, cell_type):
            threshold = self.get_warning_threshold(cell_type)
            return utilization >= threshold

    analyzer = SimpleAnalyzer()

    # 测试小区类型识别
    test_cells = [
        "测试ZFH小区001",
        "测试10D小区002",
        "测试MIMO小区003",
        "普通小区004"
    ]

    print("\n小区类型识别测试:")
    for cell in test_cells:
        cell_type = analyzer.get_cell_type(cell)
        threshold = analyzer.get_warning_threshold(cell_type)
        print(f"{cell} -> {cell_type} (阈值: {threshold:.0%})")

    # 测试预警判断
    print("\n预警判断测试:")
    test_cases = [
        ("FDD小区ZFH001", 0.75),
        ("3D-MIMO小区10D001", 0.85),
        ("普通小区001", 0.55),
        ("普通小区002", 0.45)
    ]

    for cell_name, utilization in test_cases:
        cell_type = analyzer.get_cell_type(cell_name)
        is_warning = analyzer.is_high_load_warning(utilization, cell_type)
        status = "高负荷预警" if is_warning else "正常"
        print(f"{cell_name} ({cell_type}) - 利用率: {utilization:.0%} -> {status}")

if __name__ == "__main__":
    test_file_detection()
    test_data_processing()
    print("\n测试完成！")