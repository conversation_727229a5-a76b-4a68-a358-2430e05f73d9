帮我写一个工具，统计所有Excel工作簿中小区的峰值利用率。
我的工作簿格式、数量可能不同。需要在工作表中提取相关内容。
按表格不同，分别说明：
第一种工作簿：工作表分别名为“sheet1”和“指标或计数器”，“指标或计数器”工作表无意义、无作用。在“sheet1”工作表中进行数据处理。在“sheet1”工作表中的“开始时间”表头列，提取日期。这一列的数据格式举例：2025-07-30 00:00:00，其实日期就是2025/7/30。在寻找表头名为”小区名称“这一列，是我要查询的具体对象。再查找3列，表头分别为”上行PRB平均利用率(集团)“、”下行PRB平均利用率(集团)“和”CCE利用率(PDCCH信道CCE占用率)“。
具体的处理逻辑：我要提取每个小区（”小区名称“列的内容）每个日期（”开始时间“中提取日期）的峰值利用率（”上行PRB平均利用率(集团)“、”下行PRB平均利用率(集团)“和”CCE利用率(PDCCH信道CCE占用率)“三个的最大值。

第二种工作簿：工作表分别名为“sheet0”和“指标(计数器)”，“指标(计数器)”工作表工作表无意义、无作用。在“sheet0”工作表中进行数据处理。在“sheet0”工作表中的“开始时间”表头列，提取日期。这一列的数据格式举例：2025-07-30 00:00:00，其实日期就是2025/7/30。在寻找表头名为”小区名称“这一列，是我要查询的具体对象。再查找3列，表头分别为”上行PRB平均利用率(集团)“、”下行PRB平均利用率(集团)“和”CCE利用率(PDCCH信道CCE占用率)“。
具体的处理逻辑：我要提取每个小区（”小区名称“列的内容）每个日期（”开始时间“中提取日期）的峰值利用率（”上行PRB平均利用率(集团)“、”下行PRB平均利用率(集团)“和”CCE利用率(PDCCH信道CCE占用率)“三个的最大值。

