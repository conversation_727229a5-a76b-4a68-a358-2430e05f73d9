帮我写一个工具，统计所有Excel工作簿中小区的峰值利用率。
我的工作簿格式、数量可能不同。需要在工作表中提取相关内容。
按表格不同，分别说明：
第一种工作簿：工作表分别名为“sheet1”和“指标或计数器”，“指标或计数器”工作表无意义、无作用。在“sheet1”工作表中进行数据处理。在“sheet1”工作表中的“开始时间”表头列，提取日期。这一列的数据格式举例：2025-07-30 00:00:00，其实日期就是2025/7/30。在寻找表头名为”小区名称“这一列，是我要查询的具体对象。再查找3列，表头分别为”上行PRB平均利用率(集团)“、”下行PRB平均利用率(集团)“和”CCE利用率(PDCCH信道CCE占用率)“。
具体的处理逻辑：我要提取每个小区（”小区名称“列的内容）每个日期（”开始时间“中提取日期）的峰值利用率（”上行PRB平均利用率(集团)“、”下行PRB平均利用率(集团)“和”CCE利用率(PDCCH信道CCE占用率)“三个的最大值。

第二种工作簿：工作表分别名为“sheet0”和“指标(计数器)”，“指标(计数器)”工作表工作表无意义、无作用。在“sheet0”工作表中进行数据处理。在“sheet0”工作表中的“开始时间”表头列，提取日期。这一列的数据格式举例：2025-07-30 00:00:00，其实日期就是2025/7/30。在寻找表头名为”E-UTRAN TDD小区名称“这一列，是我要查询的具体对象。再查找表头包含”峰值利用率“字段的列，这个表头名称可能是”峰值利用率-TDD-YE“或者”峰值利用率-FDD-YE"或者其他形式，但都包含“峰值利用率“。
具体的处理逻辑：我要提取每个小区（”E-UTRAN TDD小区名称“列的内容）每个日期（”开始时间“中提取日期）的”峰值利用率“。

其他要求：
1、需要创建1个新的工作表，工作表中分为以下几列：日期、小区名称、峰值利用率。将所有要处理数据的表格数据，最后汇总到这里。
2、我可能需要每个小区每个日期的峰值利用率，也可能需要每个小区几天的平均峰值利用率。如果我需要平均峰值利用率，工具需要根据原数据中所提取到的日期，询问我需要哪几天的平均峰值利用率。
3、可以导出数据为.xlsx格式文件。
4、小区名称数据中，如果包含”ZFH“三个连续的字母组合，其实就是FDD制式的小区，如果整理出的数据，FDD制式的小区，其峰值利用率大于等于70%，其则为高负荷预警小区，要把整行标记颜色，可以轻易识别出这个小区可能存在容量问题。小区名称数据中，如果包含”10D“三个连续的字符组合，或者小区名称中包含”MIMO“字符字样，它是3D-MIMO制式的小区，如果整理出的数据，3D-MIMO制式的小区，其峰值利用率大于等于80%，其则为高负荷预警小区，要把整行标记颜色，可以轻易识别出这个小区可能存在容量问题。如果小区中不包含以上特殊字符，其峰值利用率大于等于50%，为高负荷预警小区，要把整行标记颜色。可以标记相同颜色。
5、工具应该用python比较合适，但是无论哪种语言的工具，我都需要一个可视化的UI界面。简洁明了，功能完善，可视处理进度。
