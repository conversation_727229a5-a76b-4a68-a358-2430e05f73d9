@echo off
echo 启动小区峰值利用率统计工具...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查依赖库
echo 检查依赖库...
python -c "import pandas, numpy, tkinter, openpyxl, tkcalendar; print('所有依赖库已安装')" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖库...
    pip install pandas numpy openpyxl tkcalendar
    if errorlevel 1 (
        echo 错误：依赖库安装失败
        pause
        exit /b 1
    )
)

REM 启动工具
echo 启动工具...
python cell_utilization_analyzer.py

if errorlevel 1 (
    echo 工具运行出错
    pause
)