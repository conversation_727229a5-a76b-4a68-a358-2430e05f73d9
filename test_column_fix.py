#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试列名修正
"""

import pandas as pd
import glob

def test_column_extraction():
    """测试列名提取"""
    print("=== 测试列名修正 ===")

    excel_files = [f for f in glob.glob('*.xlsx') if not f.startswith('~$')]

    for file_path in excel_files:
        print(f"\n文件: {file_path}")
        try:
            xl_file = pd.ExcelFile(file_path)
            sheet_names = xl_file.sheet_names

            # 检测格式
            if 'sheet1' in sheet_names:
                print("第一种格式 - 检查sheet1的列名")
                df = pd.read_excel(file_path, sheet_name='sheet1', nrows=3)
                print(f"所有列名: {list(df.columns)}")

                # 检查关键列
                key_columns = ['开始时间', '小区', '小区名称', '上行PRB平均利用率(集团)',
                             '下行PRB平均利用率(集团)', 'CCE利用率(PDCCH信道CCE占用率)']

                for col in key_columns:
                    if col in df.columns:
                        print(f"✓ 找到列: {col}")
                        if col in ['小区', '小区名称']:
                            sample_data = df[col].dropna().head(3).tolist()
                            print(f"  样本数据: {sample_data}")
                    else:
                        print(f"✗ 未找到列: {col}")

            elif 'Sheet0' in sheet_names:
                print("第二种格式 - 检查Sheet0的列名")
                df = pd.read_excel(file_path, sheet_name='Sheet0', nrows=3)
                print(f"所有列名: {list(df.columns)}")

                # 检查关键列
                key_columns = ['开始时间', 'E-UTRAN TDD小区名称']

                for col in key_columns:
                    if col in df.columns:
                        print(f"✓ 找到列: {col}")
                        if '小区名称' in col:
                            sample_data = df[col].dropna().head(3).tolist()
                            print(f"  样本数据: {sample_data}")
                    else:
                        print(f"✗ 未找到列: {col}")

                # 查找峰值利用率列
                peak_util_cols = [col for col in df.columns if '峰值利用率' in col]
                print(f"峰值利用率相关列: {peak_util_cols}")

        except Exception as e:
            print(f"错误: {e}")

if __name__ == "__main__":
    test_column_extraction()